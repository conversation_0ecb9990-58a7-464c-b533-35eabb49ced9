import { useNavigate } from "react-router-dom";

export const SuccessPayment = () => {
  const navigate = useNavigate();
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="min-h-screen px-4 pb-32 bg-[linear-gradient(177.78deg,_#FFE5E5_24.89%,_#F5F6FE_98.13%)] flex items-center justify-center relative">
        {/* Confetti Background Pattern */}
        <div className="absolute inset-0 overflow-hidden">
          <div
            className="absolute inset-0 opacity-30 bg-repeat"
            style={{
              backgroundImage: `url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxjaXJjbGUgY3g9IjEwIiBjeT0iMTAiIHI9IjIiIGZpbGw9IiNGRkE1MDAiLz4KPGV4dCB4PSIzMCIgeT0iMjAiIHdpZHRoPSI0IiBoZWlnaHQ9IjQiIGZpbGw9IiM0RDU1RjIiLz4KPGNpcmNsZSBjeD0iNTAiIGN5PSIzMCIgcj0iMiIgZmlsbD0iI0ZGNjU2NSIvPgo8cmVjdCB4PSI3MCIgeT0iNDAiIHdpZHRoPSI0IiBoZWlnaHQ9IjQiIGZpbGw9IiM2NEQ5QTQiLz4KPGV4dCB4PSI5MCIgeT0iNTAiIHdpZHRoPSI0IiBoZWlnaHQ9IjQiIGZpbGw9IiNGRkE1MDAiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSI2MCIgcj0iMiIgZmlsbD0iIzRENTVGMiIvPgo8cmVjdCB4PSI0MCIgeT0iNzAiIHdpZHRoPSI0IiBoZWlnaHQ9IjQiIGZpbGw9IiNGRjY1NjUiLz4KPGV4dCB4PSI2MCIgeT0iODAiIHdpZHRoPSI0IiBoZWlnaHQ9IjQiIGZpbGw9IiM2NEQ5QTQiLz4KPGNpcmNsZSBjeD0iODAiIGN5PSI5MCIgcj0iMiIgZmlsbD0iI0ZGQTUwMCIvPgo8L3N2Zz4=")`,
            }}
          />
        </div>

        <div className="relative w-full max-w-[450px] mx-auto">
          <div className="relative z-20 bg-white rounded-[20px] text-center shadow-[0px_12px_120px_0px_#5F5F5F0F] overflow-hidden">
            {/* Gift Illustration Background */}
            <div className="bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] rounded-t-[20px] h-[274px] w-full flex items-center justify-center relative overflow-hidden">
              {/* Gift Box SVG */}
              <div className="relative">
                <img src="/success-svg.svg" alt="" />
              </div>
            </div>

            <div className="flex flex-col items-center text-center py-8 px-6 w-full">
              <h2 className="text-[28px] font-medium mb-2 text-[#000073] font-['Rethink_Sans'] tracking-[-0.035em]">
                You Just gifted
              </h2>
              <p className="text-[16px] text-[#808080] mb-6 font-['Rethink_Sans'] tracking-[-0.03em] leading-[1.6]">
                Olatunde successfully
              </p>

              <p className="text-[#808080] text-base mb-8 font-['Rethink_Sans']">
                You just gifted Olatunde an{" "}
                <span className="text-[#4D55F2] font-semibold">
                  Iphone 15 Pro
                </span>
              </p>

              <button
                onClick={() => navigate("/dashboard")}
                type="button"
                className="bg-[#343CD8] cursor-pointer text-base w-full max-w-[306px] text-white py-3 px-6 font-semibold rounded-full hover:bg-[#343CD8]/90 transition-colors shadow-[0px_1px_2px_0px_rgba(10,13,18,0.05)]"
              >
                <span>Back to Dashboard</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
