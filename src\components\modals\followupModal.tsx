export const FollowupModal = () => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/20 " onClick={handleClose} />
      <div className="mb-6 bg-[#F5F6FE] rounded-t-2xl flex justify-center items-center">
        <img src={wallet} alt="wallet" />
      </div>

      <div className="flex flex-col items-center text-center px-4 w-full pb-10">
        <h2 className="text-2xl md:text-[28px] font-medium my-2 text-dark-200">
          Item(s) added <br />{' '}
          <span className="text-[18px] md:text-[26px] text-grey-250">
            successfully
          </span>
        </h2>
        <p className="text-grey-250 text-base mb-7 max-w-[372px] w-full">
          Are you done curating gift items for your gift registry list? How
          would you like to proceed?{' '}
        </p>
        <button
          type="button"
          className="border cursor-pointer text-base text-white flex items-center py-2 px-6 font-semibold rounded-full gap-2 hover:bg-[#4A48E0] transition-colors">
          <span>Proceed to Cashgift</span>
        </button>
        <button
          type="button"
          className="bg-primary cursor-pointer text-base text-white flex items-center py-2 px-6 font-semibold rounded-full gap-2 hover:bg-[#4A48E0] transition-colors">
          <span>Continue adding items</span>
        </button>
      </div>
    </div>
  );
};
