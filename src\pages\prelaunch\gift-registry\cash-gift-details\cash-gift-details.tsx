import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { PageTitle } from "../../../../components/helmet/helmet";
import { useState, useEffect } from "react";
import { ArrowLeft, Gift, TickCircle } from "iconsax-react";
import stackMoney from "../../../../assets/images/stack-money2.svg";
import { Footer } from "../../footer";
import { Button } from "../../../../components/button/onboardingButton";

interface Contributor {
  id: number;
  name: string;
  email: string;
  status: "Reserved" | "Received";
}

interface CashGift {
  id: number;
  amount: number;
  description: string;
  received: number;
  contributors: Contributor[];
  amountReceived: number;
}

export const CashGiftDetails = () => {
  const { registryId, cashId } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [cashGift, setCashGift] = useState<CashGift | null>(null);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setCashGift({
        id: Number(cashId),
        amount: 6500000,
        description: "Round trip to Zanziber and Portugal",
        received: 3,
        amountReceived: 800000,
        contributors: [
          {
            id: 1,
            name: "Olivia Rhye",
            email: "<EMAIL>",
            status: "Received" as const,
          },
          {
            id: 2,
            name: "Olivia Rhye",
            email: "<EMAIL>",
            status: "Received" as const,
          },
          {
            id: 3,
            name: "Olivia Rhye",
            email: "<EMAIL>",
            status: "Received" as const,
          },
        ],
      });
      setLoading(false);
    }, 500);
  }, [registryId, cashId]);

  if (loading) {
    return <div className="flex justify-center items-center h-screen"></div>;
  }

  if (!cashGift) {
    return (
      <div className="flex justify-center items-center h-screen">
        Cash gift not found
      </div>
    );
  }

  return (
    <div className="bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]">
      <div className="max-w-[564px] mx-auto px-4 md:px-0 pb-20">
        <PageTitle
          title="Cash Gift Details"
          description="View cash gift details"
        />
        <div className="pt-8 mb-6">
          <button
            onClick={() => navigate(-1)}
            className="p-2.5 bg-white rounded-full cursor-pointer">
            <div className="bg-[#F5F6FE] w-fit p-0.5">
              <div className="bg-primary p-0.5 rounded-full">
                <ArrowLeft size="16" color="#fff" />
              </div>
            </div>
          </button>
        </div>

        <div className="">
          {/* Cash Gift Header */}
          <div className="flex pl-5 py-5 flex-wrap md:flex-nowrap gap-4 mb-6 shadow-[0px_12px_120px_0px_#5F5F5F0F] bg-white rounded-2xl ">
            <div className="flex-1">
              <div className="text-[28px] font-bold text-black mb-2">
                ₦{cashGift.amount.toLocaleString()}
              </div>
              <p className="text-base text-grey-100 mb-4">
                {cashGift.description}
              </p>
              <div className="text-sm text-green-600 mb-2 flex items-center gap-1">
                <TickCircle color="#3CC35C" size={18} variant="Bulk" />
                Crowd gifting enabled
              </div>
              {/* Progress bar */}
              <div className="w-full max-w-[228px] bg-[#FEF5F1] rounded-full h-2 ">
                <div
                  className="bg-primary h-2 rounded-full"
                  style={{ width: '50%' }}></div>
              </div>

              {/* Milestone indicators */}
              <p className="text-sm text-grey-950 mt-1">
                ₦850,000 contributed by 4 people
              </p>
              {cashGift.received > 0 && (
                <div className="flex items-center gap-1 mt-4 w-fit font-bold italic bg-green-100 text-green-700 text-sm px-2.5 py-1.5 rounded-full">
                  <span>✓ Received • {cashGift.received}</span>
                </div>
              )}
            </div>
            <div className="flex flex-wrap gap-4 mb-6">
              <img
                src={stackMoney}
                alt="Money Stack"
                className="w-[189px] h-[160px] object-contain"
              />
            </div>
          </div>

          {/* Amount Received */}
          <div className="mb-6 bg-white px-4 py-4 rounded-[16px]">
            <div className="text-[32px] font-bold text-black mb-1 italic">
              {cashGift.amountReceived.toLocaleString()}
            </div>
            <div className="text-xs font-medium text-grey-250 uppercase tracking-[0.10em]">
              AMOUNT RECEIVED
            </div>
          </div>

          {/* Contributors Section */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Contributors</h4>
            <div className="space-y-3">
              {cashGift.contributors.map((contributor) => (
                <div
                  key={contributor.id}
                  className="flex items-center justify-between p-3 bg-white rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-[#F5F6FE] rounded-full flex items-center justify-center">
                      <span className="text-primary text-base  font-semibold">
                        OR
                      </span>
                    </div>
                    <div>
                      <div className=" text-[#00008C] font-semibold text-sm">
                        {contributor.name}
                      </div>
                      <div className="text-[12px] text-[#535862]">
                        {contributor.email}
                      </div>
                    </div>
                  </div>
                  <div className="text-sm font-medium text-green-600 italic">
                    {contributor.status}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Reserve Cash Gift Button */}
          <div className="mt-8 pt-6 border-t border-grey-150">
            <Button
              variant="primary"
              size="md"
              className="text-white bg-primary-650 w-full"
              iconLeft={<Gift size="20" color="#fff" variant="Bulk" />}
              onClick={() =>
                navigate(`/gift-registry/${registryId}/cash/${cashId}/reserve`)
              }>
              Reserve Cash Gift
            </Button>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};
