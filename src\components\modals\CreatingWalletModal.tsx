import { useState, useEffect } from 'react';
import { CloseCircle } from 'iconsax-react';
import wallet from '../../assets/images/wallet.png';

interface CreateWalletModalProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: () => void;
}

export const CreateWalletModal = ({
  isOpen,
  onClose,
  onComplete,
}: CreateWalletModalProps) => {
  const [progress, setProgress] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const [showSuccessState, setShowSuccessState] = useState(false);

  console.log(progress);
  useEffect(() => {
    if (isOpen && !isCompleted) {
      setProgress(0);
      setShowSuccessState(false);

      const interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval);
            setIsCompleted(true);
            // Phase 2: Show success state after processing completes
            setTimeout(() => {
              setShowSuccessState(true);
            }, 500); // Brief pause before showing success
            return 100;
          }
          // Increment progress to complete in ~3 seconds (100 steps * 30ms = 3000ms)
          return prev + 3.33;
        });
      }, 30);

      return () => clearInterval(interval);
    }
  }, [isOpen, isCompleted]);

  useEffect(() => {
    if (!isOpen) {
      setProgress(0);
      setIsCompleted(false);
      setShowSuccessState(false);
    }
  }, [isOpen]);

  useEffect(() => {
    if (showSuccessState && onComplete) {
      const timer = setTimeout(() => {
        onComplete();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [showSuccessState, onComplete]);

  const handleClose = () => {
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/20 " onClick={handleClose} />

      <div className="relative bg-white rounded-[24px]  w-full max-w-[522px]  mx-4 z-10">
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 transition-colors">
          <CloseCircle size={40} color="#4D55F2" variant="Bulk" />
        </button>

        {!showSuccessState ? (
          <div className="text-center py-14 px-4">
            <div className="mb-10 mt-10">
              <div className="relative w-30 h-30 mx-auto mb-6 ">
                <div className="absolute inset-0 rounded-full border-6 border-grey-350"></div>
                <div
                  className="absolute inset-0 rounded-full border-6 border-transparent border-t-primary-400 animate-spin"
                  style={{
                    animation: 'spin 1s linear infinite',
                  }}></div>
              </div>
            </div>

            <h2 className="text-[28px] font-semibold text-dark-200 mb-2">
              Creating your Wallet
            </h2>
          </div>
        ) : (
          <>
            <div className="mb-6 bg-[#F5F6FE] rounded-t-2xl flex justify-center items-center">
              <img src={wallet} alt="wallet" />
            </div>

            <div className="flex flex-col items-center text-center px-4 w-full pb-10">
              <h2 className="text-2xl md:text-[28px] font-medium my-2 text-dark-200">
                Your Wallet has Successfully <br />{' '}
                <span className="text-[18px] md:text-[26px] text-grey-250">
                  been created
                </span>
              </h2>
              <p className="text-grey-250 text-base mb-7 max-w-[372px] w-full">
                Click the button below to continue setting up your gift registry
              </p>
              <button
                type="button"
                onClick={() => onComplete && onComplete()}
                className="bg-primary cursor-pointer text-base text-white flex items-center py-2 px-6 font-semibold rounded-full gap-2 hover:bg-[#4A48E0] transition-colors">
                <span>Continue Setup</span>
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
