import { ArrowCircleLeft2, Tag2 } from "iconsax-react";
import { But<PERSON> } from "../../../../components/button/onboardingButton";
import phone from "../../../../assets/images/phone-img.png";
import { StepProgress } from "../../../../components/step-progress/step-progress";
import { useState } from "react";
import { GifterDetails } from "./gifter-details";
import { GiftReservations } from "./gift-reservations";
// import { JumiaRedirect } from './redirect-jumia';

export const ItemsWithCash = () => {
  const [activeStep, setActiveStep] = useState(2);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  const steps = [
    { id: 1, name: "Gifter's details" },
    { id: 2, name: "Gift Reservation" },
  ];

  const handleStepChange = (stepId: number) => {
    if (
      completedSteps.includes(stepId) ||
      stepId === activeStep ||
      stepId === activeStep - 1
    ) {
      setActiveStep(stepId);
    }
  };
  return (
    <div className=" bg-[linear-gradient(177.78deg,_var(--color-cus-pink)_24.89%,_var(--color-primary-150)_98.13%)]  pb-36">
      <div className="pt-14 mx-auto max-w-[696px] w-full px-2 md:px-0">
        <Button
          variant="primary"
          size="md"
          //   onClick={handleComplete}
          className="text-primary-650 bg-white"
          iconLeft={
            <ArrowCircleLeft2 size="20" color="#4D55F2" variant="Bulk" />
          }
        >
          Back
        </Button>
        <div className="mt-5 md:flex justify-between bg-white rounded-2xl ">
          <div className="md:p-4 p-2 text-center md:text-start border-r border-grey-150 flex flex-col items-center md:items-start justify-between ">
            <div>
              <img src={phone} alt="gift-item" />
              <p className="text-[22px] font-medium text-grey-750 mt-5.5">
                Iphone 15 Pro
              </p>
              <p className="text-base text-grey-100">Apple's iOS iphone 15</p>
              <div className="mt-3 mx-auto md:mx-0 flex items-center gap-1.5 bg-light-blue-150 w-fit py-1.5 px-2.5 rounded-2xl">
                <Tag2 size={12} variant="Bulk" color="#5856D6 " />
                <span className="text-perple-50 text-sm font-bold">
                  ₦1,650,000
                </span>
              </div>
            </div>
            <div className="">
              <p className="text-sm text-grey-100">Total Amount</p>
              <p className="text-base font-medium text-grey-750">
                ₦3,800,000.00
              </p>
            </div>
          </div>
          <div className=" flex-1 pt-6 ">
            <div className="md:px-5 px-1">
              <h2 className="text-[22px] font-medium text-grey-750">
                Reserve a gift for Olatunde
              </h2>
              <p className="text-base text-grey-100">
                Flawless makeup for your big day.
              </p>
            </div>
            <StepProgress
              steps={steps}
              activeStep={activeStep}
              completedSteps={completedSteps}
              className="md:pl-4 !gap-1"
              onStepChange={handleStepChange}
            />{" "}
            {activeStep === 1 && (
              <GifterDetails
                onContinue={() => {
                  setCompletedSteps((prev) => [...new Set([...prev, 1])]);
                  setActiveStep(2);
                }}
              />
            )}
            {/* {activeStep === 2 && <JumiaRedirect />} */}
            {activeStep === 2 && <GiftReservations />}
          </div>
        </div>
      </div>{" "}
    </div>
  );
};
