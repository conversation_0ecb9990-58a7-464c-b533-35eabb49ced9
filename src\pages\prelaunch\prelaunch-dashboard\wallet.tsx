import React, { useState } from "react";
import { Card } from "../../../components/ui/card";

const WALLET_BALANCE = 120025;

const formatCurrency = (amount: number) =>
  `₦${amount.toLocaleString("en-NG", { maximumFractionDigits: 0 })}`;

const Wallet: React.FC = () => {
  const [activeTab, setActiveTab] = useState<"transactions" | "withdrawal">(
    "transactions"
  );

  return (
    <div
      className="w-full min-h-screen bg-gradient-to-b from-[#FEFAF8] to-[#F5F6FE]"
      style={{ minHeight: "100dvh", marginTop: 90 }}
    >
      <div className=" px-4 ">
        {/* Wallet Heading */}
        <h1
          className="text-[24px] pt-[90px] font-semibold max-w-[800px] mx-auto leading-[1] tracking-[-0.03em] text-[#000] mb-6 mt-0"
          style={{ fontFamily: "Rethink Sans" }}
        >
          Wallet
        </h1>
        {/* Balance Card */}
        <div className="bg-white rounded-2xl shadow-[0_12px_120px_0_rgba(95,95,95,0.06)]  px-0 py-0 min-h-[174px] overflow-visible relative">
          <div className="max-w-[800px] flex flex-row items-center mx-auto gap-8 px-8 py-8">
            {/* SVG Image */}
            <div
              className="flex-shrink-0 flex items-center justify-center"
              style={{ minWidth: 104 }}
            >
              <svg
                width="181"
                height="104"
                viewBox="0 0 181 104"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clip-path="url(#clip0_11404_191009)">
                  <rect width="181" height="104" rx="16" fill="#000073" />
                  <path
                    d="M173.808 120.792L177.556 107.001C161.955 100.962 133.508 92.1686 109.886 96.8954C102.807 88.5324 94.4173 81.4404 85.0446 76.034C68.5922 66.5491 50.0641 62.6118 32.1574 64.6902C23.7703 51.011 11.9037 41.0487 -0.229476 38.3844L-2.41681 52.6447C4.77417 54.2258 12.2013 59.7936 18.2857 67.5345C-7.50161 75.2676 -13.0824 92.0927 -15.0237 97.9294C-21.0942 116.191 -14.9208 134.568 -0.354764 141.595C10.3009 146.719 25.1547 146.423 34.3098 134.199C43.8922 121.396 46.3188 103.384 40.9762 84.769C40.386 82.7115 39.7191 80.675 38.9671 78.6903C60.0117 77.9113 80.6461 86.5253 96.0867 101.665C86.1031 106.912 78.058 115.992 73.8588 130.709C73.8088 130.893 73.7547 131.093 73.7047 131.276C69.7853 145.696 72.9676 161.764 81.6946 171.425C91.454 182.249 106.538 183.82 117.574 175.17C130.199 165.271 134.335 145.668 127.638 127.495C125.403 121.413 122.623 115.612 119.363 110.167C120.331 110.101 121.309 110.056 122.312 110.021C141.501 109.489 162.816 116.538 173.82 120.797L173.808 120.792ZM105.661 112.857C110.074 119.019 113.757 125.881 116.489 133.324C120.089 143.101 119.755 156.08 110.867 163.038C104.446 168.072 95.6376 167.118 89.9214 160.79C84.6223 154.911 82.7158 144.435 85.3224 135.367C88.5542 124.052 95.2487 116.6 105.648 112.852L105.661 112.857ZM26.0734 80.3382C27.4337 83.2697 28.577 86.3204 29.4733 89.4449C31.7555 97.3907 34.2313 112.508 25.2497 124.511C20.0417 131.454 10.6872 131.254 4.18582 128.127C-5.45063 123.483 -6.73002 112.318 -3.72633 103.251C0.260663 91.2454 9.73938 83.94 26.0776 80.3229L26.0734 80.3382Z"
                    fill="#4D55F2"
                  />
                  <path
                    d="M347.619 115.153L355.291 103.911C338.778 87.8619 308.128 60.7947 279.753 50.2339C273.627 37.3819 265.681 24.9433 256.193 13.5399C239.535 -6.47197 219.138 -22.328 197.994 -31.8242C191.652 -50.7863 180.396 -68.3119 167.06 -78.7804L161.073 -66.0662C168.977 -59.8598 176.184 -49.5519 181.315 -37.958C149.715 -46.9381 139.193 -33.8731 135.538 -29.3442C124.103 -15.1726 126.752 7.01491 141.832 23.375C152.868 35.3266 170.06 44.618 183.582 38.4175C197.738 31.9183 204.912 15.6421 203.279 -6.24427C203.098 -8.66329 202.825 -11.1109 202.44 -13.562C226.885 -0.753918 248.573 21.0939 262.69 46.054C249.909 44.8092 238.429 48.6118 230.013 60.481C229.911 60.6309 229.8 60.7933 229.697 60.9432C221.676 72.6976 221.439 90.6673 229.149 105.869C237.767 122.888 254.77 134.177 269.592 132.73C286.548 131.072 296.079 114.323 292.777 91.9996C291.679 84.5329 289.885 76.9926 287.451 69.4954C288.583 70.0556 289.721 70.6416 290.885 71.2539C313.131 83.1097 335.985 103.846 347.633 115.166L347.619 115.153ZM271.005 63.3181C274.594 72.2701 277.171 81.4446 278.511 90.5789C280.285 102.587 276.745 115.228 264.81 116.385C256.187 117.229 246.266 110.6 241.215 100.643C236.537 91.3999 236.885 79.7923 242.093 72.492C248.568 63.3687 258.095 60.3072 270.991 63.3049L271.005 63.3181ZM187.179 -20.2499C188.035 -16.4683 188.611 -12.7086 188.885 -9.03524C189.584 0.30813 188.764 16.88 175.495 22.9738C167.805 26.4908 157.072 20.2563 150.339 12.9643C140.361 2.1451 141.6 -9.74004 147.265 -16.7829C154.778 -26.1023 167.478 -27.2223 187.188 -20.2624L187.179 -20.2499Z"
                    fill="#4D55F2"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_11404_191009">
                    <rect width="181" height="104" rx="16" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </div>
            {/* Balance Text & Amount */}
            <div
              className="flex flex-col items-start flex-shrink-0"
              style={{ minWidth: 220 }}
            >
              <span
                className="text-[#666] text-[16px] font-normal leading-[1.35] tracking-[-0.03em] mb-1"
                style={{ fontFamily: "Rethink Sans" }}
              >
                Available Balance
              </span>
              <span
                className="text-[#000] text-[52px] font-semibold leading-[1.23] tracking-[-0.03em] mb-0"
                style={{ fontFamily: "Rethink Sans" }}
              >
                {formatCurrency(WALLET_BALANCE)}
                <span className="text-[32px] align-normal font-normal">
                  .00
                </span>
              </span>
            </div>
            {/* Actions */}
            <div className="flex flex-row gap-8 items-end ml-auto">
              {/* Top-up */}
              <div className="flex flex-col items-center gap-2">
                <div className="w-[56px] h-[56px] rounded-[16px] bg-[#F5F6FE] flex items-center justify-center mb-1">
                  <img
                    src="/assets/wallet/wallet-topup-icon.svg"
                    alt="Top-up"
                    className="w-[56px] h-[56px]"
                  />
                </div>
                <span
                  className="text-[#000073] text-[14px] font-normal leading-[1.2]"
                  style={{ fontFamily: "Rethink Sans" }}
                >
                  Top-up
                </span>
              </div>
              {/* Withdraw */}
              <div className="flex flex-col items-center gap-2">
                <div className="w-[56px] h-[56px] rounded-[16px] bg-[#F5F6FE] flex items-center justify-center mb-1">
                  <img
                    src="/assets/wallet/wallet-withdraw-icon.svg"
                    alt="Withdraw"
                    className="w-[56px] h-[56px]"
                  />
                </div>
                <span
                  className="text-[#000073] text-[14px] font-normal leading-[1.2]"
                  style={{ fontFamily: "Rethink Sans" }}
                >
                  Withdraw
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Wallet Tabs and Table Section */}
        <div className="max-w-[800px] mx-auto mt-8">
          <div className="flex items-center gap-2 mb-8">
            <div className="relative">
              <button
                className={`px-6 py-2 rounded-full font-semibold text-sm transition-all ${
                  activeTab === "transactions"
                    ? "bg-[#4D55F2] text-white"
                    : "bg-transparent text-[#666]"
                }`}
                onClick={() => setActiveTab("transactions")}
                style={{ fontFamily: "Rethink Sans" }}
              >
                Transactions
              </button>
              {activeTab === "transactions" && (
                <span className="absolute left-1/2 -bottom-2 transform -translate-x-1/2 h-1.5 w-1.5 rounded-full bg-[#FF5519]" />
              )}
            </div>
            <div className="relative">
              <button
                className={`px-6 py-2 rounded-full font-semibold text-sm transition-all ${
                  activeTab === "withdrawal"
                    ? "bg-[#4D55F2] text-white"
                    : "bg-transparent text-[#666]"
                }`}
                onClick={() => setActiveTab("withdrawal")}
                style={{ fontFamily: "Rethink Sans" }}
              >
                Withdrawal Accounts
              </button>
              {activeTab === "withdrawal" && (
                <span className="absolute left-1/2 -bottom-2 transform -translate-x-1/2 h-1.5 w-1.5 rounded-full bg-[#FF5519]" />
              )}
            </div>
          </div>
          {activeTab === "transactions" && (
            <Card className="bg-white rounded-2xl shadow-[0_12px_120px_0_rgba(95,95,95,0.06)] border-0">
              <h1 className="text-[18px] p-4 pt-5 font-medium tracking-tight">
                Transactions
              </h1>
              <div className="p-4 flex items-center justify-between">
                <div className="relative">
                  <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                    <svg
                      className="h-4 w-4 text-gray-400"
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9.58332 17.4998C13.9556 17.4998 17.5 13.9554 17.5 9.58317C17.5 5.21092 13.9556 1.6665 9.58332 1.6665C5.21107 1.6665 1.66666 5.21092 1.66666 9.58317C1.66666 13.9554 5.21107 17.4998 9.58332 17.4998Z"
                        stroke="#B3B3B3"
                        stroke-width="1.2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M18.3333 18.3332L16.6667 16.6665"
                        stroke="#B3B3B3"
                        stroke-width="1.2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </div>
                  <input
                    type="text"
                    placeholder="Search Transactions"
                    className="pl-10 pr-4 py-2 italic tracking-[-1%] w-[500px] rounded-full text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                </div>
                <button className="p-2 flex w-9 h-9 justify-center items-center rounded-full bg-[#F5F6FE] cursor-pointer hover:bg-[#e1e5fc]">
                  <svg
                    className="w-[20px] h-[20px]"
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      opacity="0.4"
                      d="M17.1664 3.41667V5.25C17.1664 5.91667 16.7497 6.75 16.333 7.16667L12.7497 10.3333C12.2497 10.75 11.9164 11.5833 11.9164 12.25V15.8333C11.9164 16.3333 11.583 17 11.1664 17.25L9.99972 18C8.91638 18.6667 7.41638 17.9167 7.41638 16.5833V12.1667C7.41638 11.5833 7.08305 10.8333 6.74972 10.4167L5.91638 9.54167L10.7664 1.75H15.4997C16.4164 1.75 17.1664 2.5 17.1664 3.41667Z"
                      fill="#4D55F2"
                    />
                    <path
                      d="M9.41695 1.75L5.10028 8.675L3.58362 7.08333C3.16695 6.66667 2.83362 5.91667 2.83362 5.41667V3.5C2.83362 2.5 3.58362 1.75 4.50028 1.75H9.41695Z"
                      fill="#4D55F2"
                    />
                  </svg>
                </button>
              </div>
              <div className="w-full max-w-[788px] mx-auto pt-6">
                <div className="overflow-x-auto">
                  <table
                    className="min-w-full text-left font-[Rethink Sans]"
                    aria-label="Wallet Transactions"
                  >
                    <thead className="bg-white text-[#535862]">
                      <tr className="text-[14px] font-medium">
                        <th className="font-medium px-[20px] py-[12px] w-[211px]">
                          Beneficiary
                        </th>
                        <th className="font-medium px-[24px] py-[12px] w-[138px]">
                          Account Number
                        </th>
                        <th className="font-medium px-[24px] py-[12px] w-[155px]">
                          Bank
                        </th>
                        <th className="font-medium px-[24px] py-[12px] w-[136px]">
                          Amount
                        </th>
                        <th className="font-medium px-[16px] py-[12px] w-[146px]">
                          Date
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white text-[#535862] text-[14px] divide-y divide-[#F0F0F0]">
                      {/* Example row, replace with map over real data */}
                      <tr className="font-normal">
                        <td className="px-[20px] py-[16px] w-[211px]">
                          Olivia Rhye
                        </td>
                        <td className="px-[24px] py-[16px] w-[138px]">
                          **********
                        </td>
                        <td className="px-[24px] py-[16px] w-[155px]">
                          Wema Bank
                        </td>
                        <td className="px-[24px] py-[16px] w-[136px]">
                          ₦320,000
                        </td>
                        <td className="px-[16px] py-[16px] w-[146px]">
                          12 February 2025
                        </td>
                      </tr>
                      <tr className="font-normal">
                        <td className="px-[20px] py-[16px] w-[211px]">
                          Olivia Rhye
                        </td>
                        <td className="px-[24px] py-[16px] w-[138px]">
                          **********
                        </td>
                        <td className="px-[24px] py-[16px] w-[155px]">
                          Union Bank
                        </td>
                        <td className="px-[24px] py-[16px] w-[136px]">
                          ₦450,000
                        </td>
                        <td className="px-[16px] py-[16px] w-[146px]">
                          15 March 2025
                        </td>
                      </tr>
                      <tr className="font-normal">
                        <td className="px-[20px] py-[16px] w-[211px]">
                          Olivia Rhye
                        </td>
                        <td className="px-[24px] py-[16px] w-[138px]">
                          **********
                        </td>
                        <td className="px-[24px] py-[16px] w-[155px]">FCMB</td>
                        <td className="px-[24px] py-[16px] w-[136px]">
                          ₦270,000
                        </td>
                        <td className="px-[16px] py-[16px] w-[146px]">
                          22 April 2025
                        </td>
                      </tr>
                      <tr className="font-normal">
                        <td className="px-[20px] py-[16px] w-[211px]">
                          Olivia Rhye
                        </td>
                        <td className="px-[24px] py-[16px] w-[138px]">
                          **********
                        </td>
                        <td className="px-[24px] py-[16px] w-[155px]">
                          First Bank
                        </td>
                        <td className="px-[24px] py-[16px] w-[136px]">
                          ₦500,000
                        </td>
                        <td className="px-[16px] py-[16px] w-[146px]">
                          30 May 2025
                        </td>
                      </tr>
                      <tr className="font-normal">
                        <td className="px-[20px] py-[16px] w-[211px]">
                          Olivia Rhye
                        </td>
                        <td className="px-[24px] py-[16px] w-[138px]">
                          **********
                        </td>
                        <td className="px-[24px] py-[16px] w-[155px]">
                          Skye Bank
                        </td>
                        <td className="px-[24px] py-[16px] w-[136px]">
                          ₦350,000
                        </td>
                        <td className="px-[16px] py-[16px] w-[146px]">
                          7 June 2025
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </Card>
          )}
          {activeTab === "withdrawal" && (
            <div className="max-w-[763px] mx-auto  rounded-2xl  p-6 flex flex-col gap-5">
              <div className="flex items-end justify-between gap-4">
                <h1
                  className="text-[18px] font-medium text-[#090909] tracking-tight"
                  style={{ fontFamily: "Rethink Sans" }}
                >
                  Withdrawal Accounts
                </h1>
                <button
                  className="flex items-center bg-[#4D55F2] text-white rounded-full shadow px-4 py-2 font-semibold text-sm gap-2 border border-[#4D55F2]"
                  style={{ borderRadius: 64 }}
                >
                  Add New
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      opacity="0.4"
                      d="M9.9974 18.3346C14.5998 18.3346 18.3307 14.6037 18.3307 10.0013C18.3307 5.39893 14.5998 1.66797 9.9974 1.66797C5.39502 1.66797 1.66406 5.39893 1.66406 10.0013C1.66406 14.6037 5.39502 18.3346 9.9974 18.3346Z"
                      fill="white"
                    />
                    <path
                      d="M13.3609 9.56016L10.8609 7.06016C10.6193 6.81849 10.2193 6.81849 9.9776 7.06016C9.73594 7.30182 9.73594 7.70182 9.9776 7.94349L11.4109 9.37682H7.08594C6.74427 9.37682 6.46094 9.66016 6.46094 10.0018C6.46094 10.3435 6.74427 10.6268 7.08594 10.6268H11.4109L9.9776 12.0602C9.73594 12.3018 9.73594 12.7018 9.9776 12.9435C10.1026 13.0685 10.2609 13.1268 10.4193 13.1268C10.5776 13.1268 10.7359 13.0685 10.8609 12.9435L13.3609 10.4435C13.6026 10.2018 13.6026 9.80182 13.3609 9.56016Z"
                      fill="white"
                    />
                  </svg>
                </button>
              </div>
              <div className="flex flex-col gap-3 mt-2">
                {/* Example account cards, replace with map over real data */}
                <div className="flex justify-between items-end bg-white rounded-[10px] shadow-[0_12px_120px_0_rgba(95,95,95,0.06)] px-6 py-4 gap-[152px]">
                  <div>
                    <div
                      className="font-semibold text-[#00008C] text-[16px]"
                      style={{ fontFamily: "Rethink Sans" }}
                    >
                      Olivia Rhye
                    </div>
                    <div className="mt-2">
                      <div
                        className="text-[10px] text-[#B3B3B3] font-normal tracking-[-0.02em]"
                        style={{ fontFamily: "Rethink Sans" }}
                      >
                        Account number
                      </div>
                      <div
                        className="text-[14px] text-[#000] font-normal"
                        style={{ fontFamily: "Rethink Sans" }}
                      >
                        **********
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <div className="flex items-center gap-2">
                      <img
                        src="/assets/banks/bank-gtbank.svg"
                        alt="GT Bank"
                        className="w-8 h-8"
                      />
                      <div>
                        <div
                          className="text-[10px] text-[#B3B3B3] font-normal"
                          style={{ fontFamily: "Rethink Sans" }}
                        >
                          Bank name
                        </div>
                        <div
                          className="text-[14px] text-[#000] font-normal"
                          style={{ fontFamily: "Rethink Sans" }}
                        >
                          GT Bank
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex justify-between items-end bg-white rounded-[10px] shadow-[0_12px_120px_0_rgba(95,95,95,0.06)] px-6 py-4 gap-[152px]">
                  <div>
                    <div
                      className="font-semibold text-[#00008C] text-[16px]"
                      style={{ fontFamily: "Rethink Sans" }}
                    >
                      Olivia Rhye
                    </div>
                    <div className="mt-2">
                      <div
                        className="text-[10px] text-[#B3B3B3] font-normal tracking-[-0.02em]"
                        style={{ fontFamily: "Rethink Sans" }}
                      >
                        Account number
                      </div>
                      <div
                        className="text-[14px] text-[#000] font-normal"
                        style={{ fontFamily: "Rethink Sans" }}
                      >
                        **********
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <div className="flex items-center gap-2">
                      <img
                        src="/assets/banks/bank-polaris.svg"
                        alt="Polaris Bank"
                        className="w-8 h-8"
                      />
                      <div>
                        <div
                          className="text-[10px] text-[#B3B3B3] font-normal"
                          style={{ fontFamily: "Rethink Sans" }}
                        >
                          Bank name
                        </div>
                        <div
                          className="text-[14px] text-[#000] font-normal"
                          style={{ fontFamily: "Rethink Sans" }}
                        >
                          Polaris Bank
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Wallet;
